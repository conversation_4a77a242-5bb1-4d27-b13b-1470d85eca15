package com.hailiang.composition.ui.widget

import com.hailiang.composition.ui.practice.AiSectorComment
import com.hailiang.composition.ui.practice.AiSubSector

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/11 13:56
 */
internal object CompositionMockData {
    fun mockAiSubSectorList(): List<AiSubSector> {
        return listOf(
            AiSubSector.Advantage(
                comments = emptyList()
            ),
            AiSubSector.Advantage(
                comments = listOf(
                    AiSectorComment.Advantage(
                        comment = "11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                    ),
                )
            ),
            AiSubSector.Suggestion(
                comments = listOf(
                    AiSectorComment.Suggestion(comment = "Suggestion")
                )
            ),
            AiSubSector.Title(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Title Advantage"),
                    AiSectorComment.Suggestion(comment = "Title Suggestion")
                )
            ),
            AiSubSector.Structure(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Structure AdvantageStructure AdvantageStructure AdvantageStructure AdvantageStructure AdvantageStructure 123123123123123123123123123123123123123123123123123123123112312312312312312312312312312312312312312312312312312323123123123123123123123123123123123123123123123123123123123123123123"),
                    AiSectorComment.Suggestion(comment = "Structure Suggestion")
                )
            ),
            AiSubSector.Argument(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Argument Advantage"),
                    AiSectorComment.Suggestion(comment = "Argument Suggestion")
                )
            ),
            AiSubSector.BeginningAndEnd(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "BeginningAndEnd Advantage"),
                    AiSectorComment.Suggestion(comment = "BeginningAndEnd Suggestion")
                )
            ),
            AiSubSector.Sentence(
                comments = listOf(
                    AiSectorComment.Advantage(
                        content = "第一句",
                        comment = "**结尾**：\n文章在第四段开头处中断（“古今盛衰，历朝历代皆有众多仁人志士为国家抛头颅，洒热血。”），缺少完整的结尾段落。一个有力的结尾对演讲稿至关重要哦！它不仅能总结全文的核心观点，升华“追求理想”的主题，还能再次点燃听众（读者）的情感，呼应标题中的“烛”与“星”，并自然收束全文。我们可以思考，如何用简练而富有感染力的话，把“修身、矫思、立义、行动”的启示与当代青年的责任担当结合起来，给演讲画上一个完美的句号呢？\n# 句子分析\n-   第1句：**大家好！今天我演讲的题目是《秉信念之烛，追时代之星》。**\n**表扬**：开篇简洁有力，符合演讲稿的规范，清晰点明主题，让听众/读者立刻知道演讲的核心内容。格式规范，值得肯定！\n-   第2句：**相信在座的友人们都有着热忠的偶像，他们或是出现在荧屏上，或是绽放在绚丽的舞台，或歌唱，或表演，都有着其个人魅力。**\n**点评**：句子试图从生活现象切入，拉近与听众距离，这个意图很好。但“友人们”的称呼稍显书面化，口语化演讲中更常用“同学们”或“朋友们”。“热忠”应为“热衷”的笔误。后半句“或歌唱，或表演”的列举与“荧屏上”、“绚丽舞台”有部分重复，且“都有着其个人魅力”稍显笼统。\n**引导**：演讲的语言可以更贴近日常交流哦！想想我们平时和同学说话会用“友人们”吗？把“热忠”改成“热衷”就更准确了。另外，描述偶像时，能否用更具体、更有画面感的词语来展现他们的魅力呢？这样会更吸引听众。\n-   第3句：**但我们更需要以理想信念为梯，摘取时代之星。**\n**表扬**：这句话成功实现了转折，从“追娱乐偶像”自然过渡到核心观点“追时代之星”，并且运用了“梯”与“摘星”的比喻，与标题呼应，形象地表达了追求理想需要凭借（理想信念）和行动（摘取），点题清晰有力！比喻运用恰当，过渡自然，是文章的亮点句子。\n-   第4句：**时代之星，是群星荟萃，星光璀璨，体恤于民，扎根于民，奉献于民的星。**\n**点评**：句子运用了排比（“体恤于民，扎根于民，奉献于民”）和比喻（“群星荟萃，星光璀璨”），努力营造气势和定义“时代之星”，值得肯定！但“群星荟萃，星光璀璨”主要形容数量多和光芒亮，与后文具体定义（体恤、扎根、奉献）的衔接稍显跳跃，逻辑上可以更紧密。\n**引导**：排比用得好，能增强气势！不过，开头的“群星荟萃，星光璀璨”主要是描绘景象，后面的“体恤于民…”是定义内涵，它们之间如何更自然地连接起来呢？比如，是否可以说“时代之星，他们或许身处不同领域，如群星闪耀，但其共同的光芒在于体恤于民…”？这样逻辑会更顺畅。\n-   第5句：**不是他院士级的学位，也不是他获得“共和国勋章”的荣誉吸引了我，而是他不辞辛苦再三出任疫情防控指导专家的行动，是他疲惫地小憩于列车之上的动人照片，更是他那一句“不要去武汉”的温情话语令我钦佩，使我奋发。**\n**表扬**：这个长句运用了“不是…也不是…而是…更是…”的递进式排比结构，情感层层推进，强烈地突出了真正打动作者的是人物的具体行动和奉献精神，而非外在光环。列举的事例（出任专家、列车小憩、温情话语）非常具体、生动、感人，极具画面感和说服力，是文章中最富有感染力的论据支撑句！情感真挚，细节动人，排比有力。\n-   第6句：**同学们是否此时此刻也被这温柔“星”光所感化呢？要追这颗“星”，同学们应脚踏实地，坚定青春理想与信念，用你们富有朝气的面庞，有力的四肢，敏捷的思维去追逐，去奋力奔跑！**\n**点评**：句子前半句尝试与听众互动（“是否…感化呢？”），符合演讲特点，后半句发出行动号召，方向正确。但“温柔‘星’光”与上文描述的“不辞辛苦”、“疲惫”等形象稍有不完全契合之处，“感化”一词力度也稍弱。号召部分“脚踏实地，坚定青春理想与信念”稍显概括，而“面庞、四肢、思维”的表述比较具象，两者结合可以更圆融。号召行动与题目材料要求的“修身、矫思、立义”的关联性可以更明确。\n**引导**：互动和号召的意识很棒！不过，“温柔星光”用来形容这位专家辛苦奔波的行动，你觉得贴切吗？有没有更精准的词？另外，号召大家用“面庞、四肢、思维”去奔跑，这个画面很生动！但材料中提到的追求理想需要“修身”（比如提升品格、知识）、“矫思”（比如端正态度、明确方向），这些具体的“行动准备”如何在号召中体现出来呢？把材料和我们的行动号召结合得更紧密些，说服力会更强哦！\n-   第7句：**这时代之星，是爱国家爱党，拥有炽热的心与拼搏顽强的星。**\n**点评**：此句试图提出“时代之星”的另一个特质（爱国爱党、拼搏顽强），方向正确。但句子独立存在，与上文（抗疫专家事例）和下文的联系（引出的历史人物）都因文章中断而显得突兀。缺少必要的过渡和解释，显得比较孤立。\n**引导**：提出“时代之星”爱国爱党、拼搏顽强的特点很好！不过，这个观点和前面详细描述的抗疫专家例子是什么关系呢？是补充说明同一个“星”的其他特质，还是引出另一类“星”？需要在上下文里交代清楚，让读者明白它的作用，这样文章思路才连贯哦。另外，如何用具体事例来展现“爱国家爱党”和“拼搏顽强”呢？\n``"
                    ),
                    AiSectorComment.Suggestion(
                        content = "第二句",
                        comment = "Sentence Suggestion"
                    )
                )
            ),
            AiSubSector.History(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "History Advantage"),
                    AiSectorComment.Suggestion(comment = "History Suggestion")
                )
            ),
            AiSubSector.Reality(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Reality Advantage"),
                    AiSectorComment.Suggestion(comment = "Reality Suggestion")
                )
            ),
            AiSubSector.RefinedSentence(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "RefinedSentence Advantage"),
                    AiSectorComment.Suggestion(comment = "RefinedSentence Suggestion")
                )
            )
        )
    }
}