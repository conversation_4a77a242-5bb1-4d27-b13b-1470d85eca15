package com.hailiang.composition.ui.widget

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.hailiang.composition.ui.practice.AiResponseState

@Composable
fun CompositionQuestionTopicLayout(
    aiResponseState: AiResponseState,
    retakePhotoAction: () -> Unit
) {
    if (aiResponseState is AiResponseState.Default) {
        OcrLoadingAnimation(progress = 0, hint = "题目识别中...")
    } else if (aiResponseState is AiResponseState.OcrLoading && aiResponseState.isTopic) {
        OcrLoadingAnimation(progress = aiResponseState.progress, hint = "题目识别中...")
    } else if (aiResponseState is AiResponseState.TitleOcrFailed) {
        RetakePlaceHolder(
            modifier = Modifier.fillMaxSize(),
            retake = retakePhotoAction,
            errorMessage = aiResponseState.errorMessage ?: "题目识别失败，请上传清晰、完整的图片哦～"
        )
    }
}