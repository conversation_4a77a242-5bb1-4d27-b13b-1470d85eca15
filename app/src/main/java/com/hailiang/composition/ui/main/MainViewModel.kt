package com.hailiang.composition.ui.main

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.hailiang.common.base.BaseViewModel
import com.hailiang.common.util.AppToast
import com.hailiang.composition.data.CompositionListWrapper
import com.hailiang.composition.data.Repository
import com.hailiang.composition.ui.main.MainActivity
import com.hailiang.composition.util.SpManager
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.getApplication
import com.hailiang.hlutil.launchCatch
import com.hailiang.textcorrection.dl.TextCorrectionManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class MainViewModel : BaseViewModel() {
    private val repository = Repository()
    private var subject = 72
    private var pageIndex = 0
    private var pageSize = 9
    val compositionListLiveData = MutableLiveData<CompositionListWrapper?>()


    fun getCompositionList(refresh: Boolean = false) {
        if (refresh) {
            pageIndex = 0
        }
        pageIndex++
        viewModelScope.launchCatch(
            errorBlock = { compositionListLiveData.value = null }
        ) {
            val data = repository.getCompositionList(pageIndex, pageSize, subject)
            if (data.isSuccess) {
                compositionListLiveData.value = data.data
            } else {
                compositionListLiveData.value = null
            }
        }
    }

    fun deleteComposition(schoolworkStateId: Long, success: () -> Unit) {
        viewModelScope.launchCatch {
            val data = repository.deleteComposition(schoolworkStateId)
            if (data.isSuccess) {
                success.invoke()
            } else {
                AppToast.toast("删除失败")
            }
        }
    }

    fun requestBeginnerGuidance(guideBlock: () -> Unit, photoBlock: () -> Unit) {
        if (SpManager.isTakePhotoBeginnerGuidanceRead()) {
            photoBlock()
            return
        }
        viewModelScope.launchCatch {
            val result = repository.getBeginnerGuidance()
            if (result.isSuccess && result.data?.isRead() == true) {
                SpManager.markTakePhotoBeginnerGuidanceRead()
                photoBlock()
                return@launchCatch
            }
            // 失败了 或者 未读，都要去引导页
            guideBlock()
        }
    }

    fun getTextCorrectionInfo(context: Context) {
        viewModelScope.launchCatch {
            val result = repository.getTextCorrectionInfo()
            if (result.isSuccess) {
                val data = result.data ?: return@launchCatch
                val url = data.getString("modelFileUrl")
                val whetherOpen = data.getString("whetherOpen")
//                if (whetherOpen == "Y") {
//
//
//                }
                if (SpManager.getTextCorrectionInfo() != url) {
                    TextCorrectionManager.deleteModel(context)
                }
                initTextCorrectionManager(context, url)
                SpManager.saveTextCorrectionInfo(url)
            }
        }
    }

    suspend fun initTextCorrectionManager(context: Context, url: String) {
        withContext(Dispatchers.IO) {
            TextCorrectionManager.initialize(
                context = context,
                url = url,
                callbackState = {
                    HLog.i(HTag.TAG, "TextCorrectionManager 初始化状态 : $it")
                },
                retry = true
            )
        }
    }
    /**
     * 获取当前页的数据
     */
    fun getCurrentIndexCompositionList() {
        // 如果此时已经是最后一页了就不需要在调用接口了
        if (pageIndex >= compositionListLiveData.value?.totalPage ?: 0) {
            return
        }
        viewModelScope.launchCatch(
            errorBlock = { compositionListLiveData.value = null }) {
            val data = repository.getCompositionList(pageIndex, pageSize, subject)
            if (data.isSuccess) {
                compositionListLiveData.value = data.data
            } else {
                compositionListLiveData.value = null
            }
        }
    }

}